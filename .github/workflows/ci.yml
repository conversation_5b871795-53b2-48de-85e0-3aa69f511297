name: "<PERSON><PERSON>"

on:
  push:
    paths-ignore:
      - "**.md"
    branches: ["develop"]
  pull_request:
    branches: ["develop"]
    paths-ignore:
      - "**.md"

jobs:
  check:
    name: cargo check
    runs-on: ubuntu-latest
    steps:
      - name: Checkout sources
        uses: actions/checkout@v3

      - name: Install stable toolchain
        uses: actions-rs/toolchain@v1
        with:
          profile: minimal
          toolchain: stable
          override: true

      - uses: Swatinem/rust-cache@v2

      - name: Run cargo check
        uses: actions-rs/cargo@v1
        with:
          command: check

  test:
    name: cargo test
    runs-on: ubuntu-latest
    steps:
      - name: Checkout sources
        uses: actions/checkout@v3

      - name: Install stable toolchain
        uses: actions-rs/toolchain@v1
        with:
          profile: minimal
          toolchain: stable
          override: true

      - uses: Swatinem/rust-cache@v2

      - name: Run cargo test
        uses: actions-rs/cargo@v1
        with:
          command: test

  lints:
    name: Lint
    runs-on: ubuntu-latest
    steps:
      - name: Checkout sources
        uses: actions/checkout@v3

      - name: Install stable toolchain
        uses: actions-rs/toolchain@v1
        with:
          profile: minimal
          toolchain: stable
          override: true
          components: rustfmt, clippy

      - uses: Swatinem/rust-cache@v2

      - name: Run cargo fmt
        uses: actions-rs/cargo@v1
        continue-on-error: false
        with:
          command: fmt
          args: --all -- --check

      - name: Run cargo clippy
        uses: actions-rs/cargo@v1
        continue-on-error: false
        with:
          command: clippy
          args: -- -D warnings
