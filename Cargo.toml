[workspace]
resolver = "3"
members = [
    "barter",
    "barter-data",
    "barter-execution",
    "barter-integration",
    "barter-macro", 
    "barter-instrument"
]

[workspace.dependencies]
# Barter Ecosystem
barter-integration = { path = "./barter-integration", version = "0.9.2" }
barter-instrument = { path = "./barter-instrument", version = "0.3.1" }
barter-data = { path = "./barter-data", version = "0.10.2" }
barter-execution = { path = "./barter-execution", version = "0.6.0" }
barter-macro = { path = "./barter-macro", version = "0.2.0" }

# Logging
tracing = { version = "0.1.41" }
tracing-subscriber = { version = "0.3.19" }

# Async
tokio = { version = "1.42", default-features = false, features = ["macros", "net", "rt-multi-thread", "signal", "time"] }
tokio-stream = { version = "0.1.17" }
tokio-test = { version = "0.4.4" }
futures = { version = "0.3.31" }
futures-util = { version = "0.3.31" }
async-trait = { version = "0.1.83" }
pin-project = { version = "1.1.7" }

# Error
thiserror = { version = "2.0.8" }

# SerDe
serde = { version = "1.0.216", features = ["derive"] }
serde_json = { version = "1.0.133" }
serde_qs = { version = "0.13.0" }
serde_urlencoded = { version = "0.7.1" }

# Protocol
url = { version = "2.5.4" }
reqwest = { version = "0.12.9",default-features = false, features = ["rustls-tls", "json"] }
tokio-tungstenite = { version = "0.26.0", features = ["url","rustls-tls-webpki-roots"] }

# Data Structures
vecmap-rs = { version = "0.2.2" }
parking_lot = { version = "0.12.3" }
rust_decimal = { version = "1.36.0", features = ["maths"] }
smol_str = { version = "0.3.2" }
fnv = { version = "1.0.7" }
indexmap = { version = "2.6.0" }

# Crytographic Signatures
hmac = { version = "0.12.1" }
sha2 = { version = "0.10.8" }
hex = { version = "0.4.3" }
base64 = { version = "0.22.1" }

# Misc
rand = { version = "0.9.0" }
chrono = { version = "0.4.39", features = ["serde"] }
derive_more = { version = "2.0.1", features = [
    "constructor",
    "display",
    "from",
] }
itertools = { version = "0.14.0" }
rust_decimal_macros = { version = "1.29.1" }
bytes = { version = "1.5.0" }
spin_sleep = { version = "1.3.0 "}
criterion = { version = "0.5.1" }
