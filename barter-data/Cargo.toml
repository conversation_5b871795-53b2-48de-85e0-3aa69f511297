[package]
name = "barter-data"
version = "0.10.2"
edition = "2024"
authors = ["JustAStream <<EMAIL>>"]
license = "MIT"
documentation = "https://docs.rs/barter-data"
repository = "https://github.com/barter-rs/barter-rs"
readme = "README.md"
description = "High performance & normalised WebSocket intergration for leading cryptocurrency exchanges - batteries included."
keywords = ["trading", "backtesting", "crypto", "stocks", "investment"]
categories = ["accessibility", "simulation"]

[dev-dependencies]
tracing-subscriber = { workspace = true, features = ["env-filter", "json"] }
rust_decimal_macros = { workspace = true }

[dependencies]
# Barter Ecosystem
barter-integration = { workspace = true }
barter-instrument = { workspace = true }
barter-macro = { workspace = true }

# Logging
tracing = { workspace = true }

# Async
tokio = { workspace = true, features = ["sync", "macros", "rt-multi-thread"] }
tokio-stream = { workspace = true, features = ["sync"] }
futures = { workspace = true }
futures-util = { workspace = true }
async-trait = { workspace = true }

# Protocol
url = { workspace = true }
reqwest = { workspace = true}

# Error
thiserror = { workspace = true }

# SerDe
serde = { workspace = true, features = ["derive"] }
serde_json = { workspace = true }

# Data Structures
parking_lot = { workspace = true }
rust_decimal = { workspace = true, features = ["serde-with-str"] }
smol_str = { workspace = true, features = ["serde"] }

# Misc
chrono = { workspace = true, features = ["serde"] }
derive_more = { workspace = true }
itertools = { workspace = true }
vecmap-rs = { workspace = true }
fnv = { workspace = true }
