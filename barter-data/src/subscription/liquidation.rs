use super::SubscriptionKind;
use barter_instrument::Side;
use chrono::{DateTime, Utc};
use serde::{Deserialize, Serialize};

/// Barter [`Subscription`](super::Subscription) [`SubscriptionKind`] that yields [`Liquidation`]
/// [`MarketEvent<T>`](crate::event::MarketEvent) events.
#[derive(
    Copy, Clone, Eq, PartialEq, Ord, PartialOrd, Hash, Debug, Default, Deserialize, Serialize,
)]
pub struct Liquidations;

impl SubscriptionKind for Liquidations {
    type Event = Liquidation;

    fn as_str(&self) -> &'static str {
        "liquidations"
    }
}

impl std::fmt::Display for Liquidations {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        write!(f, "{}", self.as_str())
    }
}

/// Normalised Barter [`Liquidation`] model.
#[derive(<PERSON><PERSON>, <PERSON><PERSON>, PartialEq, PartialOrd, Debug, Deserialize, Serialize)]
pub struct Liquidation {
    pub side: Side,
    pub price: f64,
    pub quantity: f64,
    pub time: DateTime<Utc>,
}
