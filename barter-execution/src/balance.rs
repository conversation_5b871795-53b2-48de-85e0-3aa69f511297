use chrono::{DateTime, Utc};
use derive_more::Constructor;
use rust_decimal::Decimal;
use serde::{Deserialize, Serialize};

#[derive(
    Debug, <PERSON>lone, PartialEq, Eq, PartialOrd, Ord, Hash, Deserialize, Serialize, Constructor,
)]
pub struct AssetBalance<AssetKey> {
    pub asset: AssetKey,
    pub balance: Balance,
    pub time_exchange: DateTime<Utc>,
}

#[derive(
    <PERSON>bu<PERSON>,
    <PERSON><PERSON>,
    <PERSON><PERSON>,
    PartialEq,
    Eq,
    PartialOrd,
    Or<PERSON>,
    <PERSON><PERSON>,
    Default,
    Deserialize,
    Serialize,
    Constructor,
)]
pub struct Balance {
    pub total: Decimal,
    pub free: Decimal,
}

impl Balance {
    pub fn used(&self) -> Decimal {
        self.total - self.free
    }
}
