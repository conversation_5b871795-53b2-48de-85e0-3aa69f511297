[package]
name = "barter-integration"
version = "0.9.2"
edition = "2024"
authors = ["JustAStream <<EMAIL>>"]
license = "MIT"
documentation = "https://docs.rs/barter-integration"
repository = "https://github.com/barter-rs/barter-rs"
readme = "README.md"
description = "Low-level framework for composing flexible web integrations, especially with financial exchanges"
keywords = ["trading", "backtesting", "crypto", "stocks", "investment"]
categories = ["accessibility", "simulation"]

[dev-dependencies]
tokio-test = { workspace = true }
sha2 = { workspace = true }

[dependencies]
# Barter Ecosystem
barter-instrument = { workspace = true }

# Logging
tracing = { workspace = true }

# SerDe
serde = { workspace = true, features = ["derive"] }
serde_json = { workspace = true }
serde_qs = { workspace = true }
serde_urlencoded = { workspace = true }

# Error
thiserror = { workspace = true }

# Async
tokio = { workspace = true, features = [
    "net",
    "sync",
    "macros",
    "rt-multi-thread",
] }
tokio-stream = { workspace = true }
futures = { workspace = true }
pin-project = { workspace = true }

# Data Structures
indexmap = { workspace = true }
fnv = { workspace = true }
smol_str = { workspace = true, features = ["serde"]}

# Protocol
tokio-tungstenite = { workspace = true }
reqwest = { workspace = true }
url = { workspace = true }

# Cryptographic Signatures
hmac = { workspace = true }
hex = { workspace = true }
base64 = { workspace = true }

# Misc
chrono = { workspace = true, features = ["serde"] }
itertools = { workspace = true }
bytes = { workspace = true }
derive_more = { workspace = true, features = ["display", "constructor"] }
