// 截面多因子选币策略示例
// 这个示例展示了如何实现一个基于多个因子的截面选币策略
// 使用模拟数据进行演示，并集成 barter 回测框架

use barter::{
    backtest::{
        BacktestArgsConstant, BacktestArgsDynamic, market_data::MarketDataInMemory, run_backtests,
    },
    engine::{
        Processor,
        state::{
            EngineState,
            builder::EngineStateBuilder,
            global::DefaultGlobalData,
            instrument::{
                data::{DefaultInstrumentMarketData, InstrumentDataState},
                filter::InstrumentFilter,
            },
        },
    },
    risk::DefaultRiskManager,
    statistic::time::Daily,
    strategy::{
        algo::AlgoStrategy,
        close_positions::{ClosePositionsStrategy, close_open_positions_with_market_orders},
        on_disconnect::OnDisconnectStrategy,
        on_trading_disabled::OnTradingDisabled,
    },
    system::config::SystemConfig,
};
use barter_data::{
    event::{DataKind, MarketEvent},
    streams::consumer::MarketStreamEvent,
};
use barter_execution::{
    AccountEvent,
    order::{
        OrderKind, TimeInForce,
        id::{ClientOrderId, StrategyId},
        request::{OrderRequestCancel, OrderRequestOpen, RequestOpen},
    },
};
use barter_instrument::{
    Side, asset::AssetIndex, exchange::ExchangeIndex, instrument::InstrumentIndex,
};
use rust_decimal::{Decimal, MathematicalOps};
use rust_decimal_macros::dec;
use serde::{Deserialize, Serialize};
use tracing::info;

/// 因子数据结构
#[derive(Debug, Clone, Default, Serialize, Deserialize)]
pub struct FactorData {
    /// 动量因子（价格变化率）
    pub momentum: Decimal,
    /// 均值回归因子（价格偏离移动平均线的程度）
    pub mean_reversion: Decimal,
    /// 成交量因子
    pub volume_factor: Decimal,
    /// 波动率因子
    pub volatility: Decimal,
}

/// 价格历史数据
#[derive(Debug, Clone, Default)]
pub struct PriceHistory {
    pub prices: Vec<Decimal>,
    pub volumes: Vec<Decimal>,
    pub max_length: usize,
}

impl PriceHistory {
    pub fn new(max_length: usize) -> Self {
        Self {
            prices: Vec::new(),
            volumes: Vec::new(),
            max_length,
        }
    }

    pub fn add_price(&mut self, price: Decimal, volume: Decimal) {
        self.prices.push(price);
        self.volumes.push(volume);

        if self.prices.len() > self.max_length {
            self.prices.remove(0);
            self.volumes.remove(0);
        }
    }

    pub fn latest_price(&self) -> Option<Decimal> {
        self.prices.last().copied()
    }
}

/// 多因子工具数据
#[derive(Debug, Clone, Default)]
pub struct MultiFactorInstrumentData {
    pub market_data: DefaultInstrumentMarketData,
    pub price_history: PriceHistory,
    pub factors: FactorData,
}

impl MultiFactorInstrumentData {
    pub fn new() -> Self {
        Self {
            market_data: DefaultInstrumentMarketData::default(),
            price_history: PriceHistory::new(50), // 保留50个历史价格
            factors: FactorData::default(),
        }
    }

    /// 计算所有因子
    pub fn calculate_factors(&mut self) {
        if self.price_history.prices.len() < 10 {
            return; // 需要足够的历史数据
        }

        self.factors.momentum = self.calculate_momentum();
        self.factors.mean_reversion = self.calculate_mean_reversion();
        self.factors.volume_factor = self.calculate_volume_factor();
        self.factors.volatility = self.calculate_volatility();
    }

    fn calculate_momentum(&self) -> Decimal {
        let prices = &self.price_history.prices;
        if prices.len() < 10 {
            return Decimal::ZERO;
        }

        let recent_price = prices[prices.len() - 1];
        let old_price = prices[prices.len() - 10];

        if old_price != Decimal::ZERO {
            (recent_price - old_price) / old_price
        } else {
            Decimal::ZERO
        }
    }

    fn calculate_mean_reversion(&self) -> Decimal {
        let prices = &self.price_history.prices;
        if prices.len() < 20 {
            return Decimal::ZERO;
        }

        let current_price = prices[prices.len() - 1];
        let ma20: Decimal = prices.iter().rev().take(20).sum::<Decimal>() / dec!(20);

        if ma20 != Decimal::ZERO {
            (ma20 - current_price) / ma20 // 价格低于均线时为正
        } else {
            Decimal::ZERO
        }
    }

    fn calculate_volume_factor(&self) -> Decimal {
        let volumes = &self.price_history.volumes;
        if volumes.len() < 10 {
            return Decimal::ZERO;
        }

        let recent_volume: Decimal = volumes.iter().rev().take(5).sum::<Decimal>() / dec!(5);
        let old_volume: Decimal = volumes.iter().rev().skip(5).take(5).sum::<Decimal>() / dec!(5);

        if old_volume != Decimal::ZERO {
            recent_volume / old_volume - Decimal::ONE
        } else {
            Decimal::ZERO
        }
    }

    fn calculate_volatility(&self) -> Decimal {
        let prices = &self.price_history.prices;
        if prices.len() < 10 {
            return Decimal::ZERO;
        }

        let returns: Vec<Decimal> = prices.windows(2).map(|w| (w[1] - w[0]) / w[0]).collect();

        if returns.is_empty() {
            return Decimal::ZERO;
        }

        let mean_return = returns.iter().sum::<Decimal>() / Decimal::from(returns.len());
        let variance = returns
            .iter()
            .map(|r| (*r - mean_return).powi(2))
            .sum::<Decimal>()
            / Decimal::from(returns.len());

        // 简化的标准差计算
        variance.sqrt().unwrap_or(Decimal::ZERO)
    }
}

/// 截面多因子选币策略
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CrossSectionalMultiFactorStrategy {
    pub id: StrategyId,
    pub max_positions: usize,
    pub momentum_weight: Decimal,
    pub mean_reversion_weight: Decimal,
    pub volume_weight: Decimal,
    pub volatility_weight: Decimal,
}

impl Default for CrossSectionalMultiFactorStrategy {
    fn default() -> Self {
        Self {
            id: StrategyId::new("cross_sectional_multi_factor"),
            max_positions: 2,
            momentum_weight: dec!(0.3),
            mean_reversion_weight: dec!(0.3),
            volume_weight: dec!(0.2),
            volatility_weight: dec!(-0.2), // 负权重：偏好低波动率
        }
    }
}

impl CrossSectionalMultiFactorStrategy {
    /// 计算综合因子得分
    fn calculate_composite_score(&self, factors: &FactorData) -> Decimal {
        self.momentum_weight * factors.momentum
            + self.mean_reversion_weight * factors.mean_reversion
            + self.volume_weight * factors.volume_factor
            + self.volatility_weight * factors.volatility
    }
}

// 实现 InstrumentDataState trait
impl InstrumentDataState for MultiFactorInstrumentData {
    type MarketEventKind = DataKind;

    fn price(&self) -> Option<Decimal> {
        self.market_data.price()
    }
}

// 实现 Processor trait for MarketEvent
impl<InstrumentKey> Processor<&MarketEvent<InstrumentKey, DataKind>> for MultiFactorInstrumentData {
    type Audit = ();

    fn process(&mut self, event: &MarketEvent<InstrumentKey, DataKind>) -> Self::Audit {
        // 委托给市场数据处理
        self.market_data.process(event);

        // 更新价格历史
        if let Some(price) = self.market_data.price() {
            // 简化的成交量处理
            let volume = match &event.kind {
                DataKind::Trade(trade) => Decimal::try_from(trade.amount).unwrap_or_default(),
                _ => dec!(1.0), // 默认成交量
            };

            self.price_history.add_price(price, volume);

            // 重新计算因子
            self.calculate_factors();
        }
    }
}

// 实现 Processor trait for AccountEvent
impl<ExchangeKey, AssetKey, InstrumentKey>
    Processor<&AccountEvent<ExchangeKey, AssetKey, InstrumentKey>> for MultiFactorInstrumentData
{
    type Audit = ();

    fn process(
        &mut self,
        event: &AccountEvent<ExchangeKey, AssetKey, InstrumentKey>,
    ) -> Self::Audit {
        self.market_data.process(event);
    }
}

// 实现 InFlightRequestRecorder trait
impl<ExchangeKey, InstrumentKey>
    barter::engine::state::order::in_flight_recorder::InFlightRequestRecorder<
        ExchangeKey,
        InstrumentKey,
    > for MultiFactorInstrumentData
{
    fn record_in_flight_cancel(
        &mut self,
        request: &OrderRequestCancel<ExchangeKey, InstrumentKey>,
    ) {
        self.market_data.record_in_flight_cancel(request);
    }

    fn record_in_flight_open(&mut self, request: &OrderRequestOpen<ExchangeKey, InstrumentKey>) {
        self.market_data.record_in_flight_open(request);
    }
}

// 实现 AlgoStrategy trait
impl AlgoStrategy for CrossSectionalMultiFactorStrategy {
    type State = EngineState<DefaultGlobalData, MultiFactorInstrumentData>;

    fn generate_algo_orders(
        &self,
        state: &Self::State,
    ) -> (
        impl IntoIterator<Item = OrderRequestCancel<ExchangeIndex, InstrumentIndex>>,
        impl IntoIterator<Item = OrderRequestOpen<ExchangeIndex, InstrumentIndex>>,
    ) {
        // 收集所有工具的因子得分
        let mut instrument_scores: Vec<(InstrumentIndex, Decimal)> = state
            .instruments
            .instruments(&InstrumentFilter::None)
            .filter_map(|inst| {
                // 确保有足够的历史数据
                if inst.data.price_history.prices.len() < 20 {
                    return None;
                }

                let score = self.calculate_composite_score(&inst.data.factors);
                Some((inst.key, score))
            })
            .collect();

        // 按得分降序排序
        instrument_scores.sort_by(|a, b| b.1.cmp(&a.1));

        // 选择前N个工具进行投资
        let selected_instruments: Vec<InstrumentIndex> = instrument_scores
            .into_iter()
            .take(self.max_positions)
            .map(|(idx, _)| idx)
            .collect();

        info!(
            "Selected instruments for investment: {:?}",
            selected_instruments
        );

        // 生成订单
        let orders: Vec<OrderRequestOpen<ExchangeIndex, InstrumentIndex>> = selected_instruments
            .into_iter()
            .filter_map(|instrument_index| {
                self.generate_order_for_instrument(state, instrument_index)
            })
            .collect();

        info!("Generated {} orders", orders.len());

        (std::iter::empty(), orders)
    }
}

impl CrossSectionalMultiFactorStrategy {
    /// 生成特定工具的订单
    fn generate_order_for_instrument(
        &self,
        state: &EngineState<DefaultGlobalData, MultiFactorInstrumentData>,
        instrument_index: InstrumentIndex,
    ) -> Option<OrderRequestOpen<ExchangeIndex, InstrumentIndex>> {
        let instrument_state = state
            .instruments
            .instruments(&InstrumentFilter::None)
            .find(|inst| inst.key == instrument_index)?;

        // 检查是否已有持仓
        if instrument_state.position.current.is_some() {
            return None;
        }

        // 获取当前价格
        let current_price = instrument_state.data.price()?;

        // 简单的仓位管理：每个标的投资固定金额
        let order_value = dec!(1000.0); // 每个标的投资1000 USDT
        let quantity = order_value / current_price;

        Some(OrderRequestOpen {
            key: barter_execution::order::OrderKey {
                exchange: ExchangeIndex(0), // 假设使用第一个交易所
                instrument: instrument_index,
                strategy: self.id.clone(),
                cid: ClientOrderId::new("multi_factor"),
            },
            state: RequestOpen {
                side: Side::Buy,
                price: current_price,
                quantity,
                kind: OrderKind::Market,
                time_in_force: TimeInForce::ImmediateOrCancel,
            },
        })
    }
}

// 实现 ClosePositionsStrategy trait
impl ClosePositionsStrategy for CrossSectionalMultiFactorStrategy {
    type State = EngineState<DefaultGlobalData, MultiFactorInstrumentData>;

    fn close_positions_requests<'a>(
        &'a self,
        state: &'a Self::State,
        filter: &'a InstrumentFilter<ExchangeIndex, AssetIndex, InstrumentIndex>,
    ) -> (
        impl IntoIterator<Item = OrderRequestCancel<ExchangeIndex, InstrumentIndex>> + 'a,
        impl IntoIterator<Item = OrderRequestOpen<ExchangeIndex, InstrumentIndex>> + 'a,
    )
    where
        ExchangeIndex: 'a,
        AssetIndex: 'a,
        InstrumentIndex: 'a,
    {
        close_open_positions_with_market_orders(&self.id, state, filter, |_| {
            ClientOrderId::new("close_position")
        })
    }
}

// 实现 OnDisconnectStrategy trait
impl<Clock, ExecutionTxs, Risk>
    OnDisconnectStrategy<
        Clock,
        EngineState<DefaultGlobalData, MultiFactorInstrumentData>,
        ExecutionTxs,
        Risk,
    > for CrossSectionalMultiFactorStrategy
{
    type OnDisconnect = ();

    fn on_disconnect(
        _engine: &mut barter::engine::Engine<
            Clock,
            EngineState<DefaultGlobalData, MultiFactorInstrumentData>,
            ExecutionTxs,
            Self,
            Risk,
        >,
        _exchange: barter_instrument::exchange::ExchangeId,
    ) -> Self::OnDisconnect {
        info!("Exchange disconnected, strategy paused");
    }
}

// 实现 OnTradingDisabled trait
impl<Clock, ExecutionTxs, Risk>
    OnTradingDisabled<
        Clock,
        EngineState<DefaultGlobalData, MultiFactorInstrumentData>,
        ExecutionTxs,
        Risk,
    > for CrossSectionalMultiFactorStrategy
{
    type OnTradingDisabled = ();

    fn on_trading_disabled(
        _engine: &mut barter::engine::Engine<
            Clock,
            EngineState<DefaultGlobalData, MultiFactorInstrumentData>,
            ExecutionTxs,
            Self,
            Risk,
        >,
    ) -> Self::OnTradingDisabled {
        info!("Trading disabled, strategy stopped");
    }
}

fn main() {
    // 初始化日志
    tracing_subscriber::fmt::init();

    info!("截面多因子选币策略示例 - 使用 Barter 回测框架");

    // 创建策略实例
    let strategy = CrossSectionalMultiFactorStrategy::default();

    info!("策略配置:");
    info!("  最大持仓数: {}", strategy.max_positions);
    info!("  动量权重: {}", strategy.momentum_weight);
    info!("  均值回归权重: {}", strategy.mean_reversion_weight);
    info!("  成交量权重: {}", strategy.volume_weight);
    info!("  波动率权重: {}", strategy.volatility_weight);

    // 模拟一些因子数据
    let mut btc_data = MultiFactorInstrumentData::new();
    let mut eth_data = MultiFactorInstrumentData::new();
    let mut sol_data = MultiFactorInstrumentData::new();

    // 添加模拟价格数据
    for i in 0..30 {
        let btc_price = dec!(50000) + Decimal::from(i * 100);
        let eth_price = dec!(3000) + Decimal::from(i * 50);
        let sol_price = dec!(100) + Decimal::from(i * 2);

        btc_data.price_history.add_price(btc_price, dec!(10.0));
        eth_data.price_history.add_price(eth_price, dec!(20.0));
        sol_data.price_history.add_price(sol_price, dec!(50.0));
    }

    // 计算因子
    btc_data.calculate_factors();
    eth_data.calculate_factors();
    sol_data.calculate_factors();

    // 计算综合得分
    let btc_score = strategy.calculate_composite_score(&btc_data.factors);
    let eth_score = strategy.calculate_composite_score(&eth_data.factors);
    let sol_score = strategy.calculate_composite_score(&sol_data.factors);

    info!("因子计算结果:");
    info!(
        "BTC - 动量: {:.4}, 均值回归: {:.4}, 成交量: {:.4}, 波动率: {:.4}, 综合得分: {:.4}",
        btc_data.factors.momentum,
        btc_data.factors.mean_reversion,
        btc_data.factors.volume_factor,
        btc_data.factors.volatility,
        btc_score
    );

    info!(
        "ETH - 动量: {:.4}, 均值回归: {:.4}, 成交量: {:.4}, 波动率: {:.4}, 综合得分: {:.4}",
        eth_data.factors.momentum,
        eth_data.factors.mean_reversion,
        eth_data.factors.volume_factor,
        eth_data.factors.volatility,
        eth_score
    );

    info!(
        "SOL - 动量: {:.4}, 均值回归: {:.4}, 成交量: {:.4}, 波动率: {:.4}, 综合得分: {:.4}",
        sol_data.factors.momentum,
        sol_data.factors.mean_reversion,
        sol_data.factors.volume_factor,
        sol_data.factors.volatility,
        sol_score
    );

    // 排序并选择最佳标的
    let mut scores = vec![("BTC", btc_score), ("ETH", eth_score), ("SOL", sol_score)];

    scores.sort_by(|a, b| b.1.cmp(&a.1)); // 按得分降序排列

    info!("截面排序结果:");
    for (i, (symbol, score)) in scores.iter().enumerate() {
        info!("  {}. {}: {:.4}", i + 1, symbol, score);
    }

    // 选择前N个进行投资
    let selected = &scores[..strategy.max_positions.min(scores.len())];
    info!(
        "选择投资标的: {:?}",
        selected.iter().map(|(s, _)| *s).collect::<Vec<_>>()
    );

    info!("注意：完整的回测需要集成 Barter 的回测框架");
    info!("这需要设置市场数据、交易所配置、风险管理等组件");
    info!("当前示例展示了策略的核心逻辑和因子计算，包含完整的 trait 实现");
    info!("策略已准备好集成到 Barter 回测系统中");
}
