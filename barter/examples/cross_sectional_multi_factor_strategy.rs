// 截面多因子选币策略示例
// 这个示例展示了如何实现一个基于多个因子的截面选币策略
// 使用模拟数据进行演示

use barter_execution::order::id::StrategyId;
use rust_decimal::{Decimal, MathematicalOps};
use rust_decimal_macros::dec;
use serde::{Deserialize, Serialize};
use tracing::info;

/// 因子数据结构
#[derive(Debug, <PERSON><PERSON>, Default, Serialize, Deserialize)]
pub struct FactorData {
    /// 动量因子（价格变化率）
    pub momentum: Decimal,
    /// 均值回归因子（价格偏离移动平均线的程度）
    pub mean_reversion: Decimal,
    /// 成交量因子
    pub volume_factor: Decimal,
    /// 波动率因子
    pub volatility: Decimal,
}

/// 价格历史数据
#[derive(Debug, <PERSON><PERSON>, Default)]
pub struct PriceHistory {
    pub prices: Vec<Decimal>,
    pub volumes: Vec<Decimal>,
    pub max_length: usize,
}

impl PriceHistory {
    pub fn new(max_length: usize) -> Self {
        Self {
            prices: Vec::new(),
            volumes: Vec::new(),
            max_length,
        }
    }

    pub fn add_price(&mut self, price: Decimal, volume: Decimal) {
        self.prices.push(price);
        self.volumes.push(volume);

        if self.prices.len() > self.max_length {
            self.prices.remove(0);
            self.volumes.remove(0);
        }
    }

    pub fn latest_price(&self) -> Option<Decimal> {
        self.prices.last().copied()
    }
}

/// 多因子工具数据
#[derive(Debug, Clone, Default)]
pub struct MultiFactorInstrumentData {
    pub price_history: PriceHistory,
    pub factors: FactorData,
}

impl MultiFactorInstrumentData {
    pub fn new() -> Self {
        Self {
            price_history: PriceHistory::new(50), // 保留50个历史价格
            factors: FactorData::default(),
        }
    }

    /// 计算所有因子
    pub fn calculate_factors(&mut self) {
        if self.price_history.prices.len() < 10 {
            return; // 需要足够的历史数据
        }

        self.factors.momentum = self.calculate_momentum();
        self.factors.mean_reversion = self.calculate_mean_reversion();
        self.factors.volume_factor = self.calculate_volume_factor();
        self.factors.volatility = self.calculate_volatility();
    }

    fn calculate_momentum(&self) -> Decimal {
        let prices = &self.price_history.prices;
        if prices.len() < 10 {
            return Decimal::ZERO;
        }

        let recent_price = prices[prices.len() - 1];
        let old_price = prices[prices.len() - 10];

        if old_price != Decimal::ZERO {
            (recent_price - old_price) / old_price
        } else {
            Decimal::ZERO
        }
    }

    fn calculate_mean_reversion(&self) -> Decimal {
        let prices = &self.price_history.prices;
        if prices.len() < 20 {
            return Decimal::ZERO;
        }

        let current_price = prices[prices.len() - 1];
        let ma20: Decimal = prices.iter().rev().take(20).sum::<Decimal>() / dec!(20);

        if ma20 != Decimal::ZERO {
            (ma20 - current_price) / ma20 // 价格低于均线时为正
        } else {
            Decimal::ZERO
        }
    }

    fn calculate_volume_factor(&self) -> Decimal {
        let volumes = &self.price_history.volumes;
        if volumes.len() < 10 {
            return Decimal::ZERO;
        }

        let recent_volume: Decimal = volumes.iter().rev().take(5).sum::<Decimal>() / dec!(5);
        let old_volume: Decimal = volumes.iter().rev().skip(5).take(5).sum::<Decimal>() / dec!(5);

        if old_volume != Decimal::ZERO {
            recent_volume / old_volume - Decimal::ONE
        } else {
            Decimal::ZERO
        }
    }

    fn calculate_volatility(&self) -> Decimal {
        let prices = &self.price_history.prices;
        if prices.len() < 10 {
            return Decimal::ZERO;
        }

        let returns: Vec<Decimal> = prices.windows(2).map(|w| (w[1] - w[0]) / w[0]).collect();

        if returns.is_empty() {
            return Decimal::ZERO;
        }

        let mean_return = returns.iter().sum::<Decimal>() / Decimal::from(returns.len());
        let variance = returns
            .iter()
            .map(|r| (*r - mean_return).powi(2))
            .sum::<Decimal>()
            / Decimal::from(returns.len());

        // 简化的标准差计算
        variance.sqrt().unwrap_or(Decimal::ZERO)
    }
}

/// 截面多因子选币策略
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CrossSectionalMultiFactorStrategy {
    pub id: StrategyId,
    pub max_positions: usize,
    pub momentum_weight: Decimal,
    pub mean_reversion_weight: Decimal,
    pub volume_weight: Decimal,
    pub volatility_weight: Decimal,
}

impl Default for CrossSectionalMultiFactorStrategy {
    fn default() -> Self {
        Self {
            id: StrategyId::new("cross_sectional_multi_factor"),
            max_positions: 2,
            momentum_weight: dec!(0.3),
            mean_reversion_weight: dec!(0.3),
            volume_weight: dec!(0.2),
            volatility_weight: dec!(-0.2), // 负权重：偏好低波动率
        }
    }
}

impl CrossSectionalMultiFactorStrategy {
    /// 计算综合因子得分
    fn calculate_composite_score(&self, factors: &FactorData) -> Decimal {
        self.momentum_weight * factors.momentum
            + self.mean_reversion_weight * factors.mean_reversion
            + self.volume_weight * factors.volume_factor
            + self.volatility_weight * factors.volatility
    }
}

fn main() {
    // 初始化日志
    tracing_subscriber::fmt::init();

    info!("截面多因子选币策略示例");

    // 创建策略实例
    let strategy = CrossSectionalMultiFactorStrategy::default();

    info!("策略配置:");
    info!("  最大持仓数: {}", strategy.max_positions);
    info!("  动量权重: {}", strategy.momentum_weight);
    info!("  均值回归权重: {}", strategy.mean_reversion_weight);
    info!("  成交量权重: {}", strategy.volume_weight);
    info!("  波动率权重: {}", strategy.volatility_weight);

    // 模拟一些因子数据
    let mut btc_data = MultiFactorInstrumentData::new();
    let mut eth_data = MultiFactorInstrumentData::new();
    let mut sol_data = MultiFactorInstrumentData::new();

    // 添加模拟价格数据
    for i in 0..30 {
        let btc_price = dec!(50000) + Decimal::from(i * 100);
        let eth_price = dec!(3000) + Decimal::from(i * 50);
        let sol_price = dec!(100) + Decimal::from(i * 2);

        btc_data.price_history.add_price(btc_price, dec!(10.0));
        eth_data.price_history.add_price(eth_price, dec!(20.0));
        sol_data.price_history.add_price(sol_price, dec!(50.0));
    }

    // 计算因子
    btc_data.calculate_factors();
    eth_data.calculate_factors();
    sol_data.calculate_factors();

    // 计算综合得分
    let btc_score = strategy.calculate_composite_score(&btc_data.factors);
    let eth_score = strategy.calculate_composite_score(&eth_data.factors);
    let sol_score = strategy.calculate_composite_score(&sol_data.factors);

    info!("因子计算结果:");
    info!(
        "BTC - 动量: {:.4}, 均值回归: {:.4}, 成交量: {:.4}, 波动率: {:.4}, 综合得分: {:.4}",
        btc_data.factors.momentum,
        btc_data.factors.mean_reversion,
        btc_data.factors.volume_factor,
        btc_data.factors.volatility,
        btc_score
    );

    info!(
        "ETH - 动量: {:.4}, 均值回归: {:.4}, 成交量: {:.4}, 波动率: {:.4}, 综合得分: {:.4}",
        eth_data.factors.momentum,
        eth_data.factors.mean_reversion,
        eth_data.factors.volume_factor,
        eth_data.factors.volatility,
        eth_score
    );

    info!(
        "SOL - 动量: {:.4}, 均值回归: {:.4}, 成交量: {:.4}, 波动率: {:.4}, 综合得分: {:.4}",
        sol_data.factors.momentum,
        sol_data.factors.mean_reversion,
        sol_data.factors.volume_factor,
        sol_data.factors.volatility,
        sol_score
    );

    // 排序并选择最佳标的
    let mut scores = vec![("BTC", btc_score), ("ETH", eth_score), ("SOL", sol_score)];

    scores.sort_by(|a, b| b.1.cmp(&a.1)); // 按得分降序排列

    info!("截面排序结果:");
    for (i, (symbol, score)) in scores.iter().enumerate() {
        info!("  {}. {}: {:.4}", i + 1, symbol, score);
    }

    // 选择前N个进行投资
    let selected = &scores[..strategy.max_positions.min(scores.len())];
    info!(
        "选择投资标的: {:?}",
        selected.iter().map(|(s, _)| *s).collect::<Vec<_>>()
    );

    info!("策略演示完成！");
}
