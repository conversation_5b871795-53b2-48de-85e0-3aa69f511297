use crate::{engine::execution_tx::MultiExchangeTxMap, execution::builder::ExecutionHandles};
use barter_data::streams::reconnect;
use barter_execution::AccountEvent;
use barter_instrument::{
    asset::AssetIndex,
    exchange::{ExchangeId, ExchangeIndex},
    instrument::InstrumentIndex,
};
use barter_integration::channel::Channel;

/// Provides an execution manager builder for ergonomically initialising multiple execution links
/// to mock and live exchanges.
pub mod builder;

/// Provides an error type that represents all errors that are generated by an execution link.
pub mod error;

/// Per-exchange execution manager that actions order requests from the Engine and forwards back
/// responses.
pub mod manager;

/// Defines an `ExecutionRequest` used by the `Engine` to communicate with an `ExecutionManager`.
pub mod request;

/// Convenient type alias that represents a [`reconnect::Event`] produced by the [`AccountEvent`]
/// stream.
pub type AccountStreamEvent<
    ExchangeKey = ExchangeIndex,
    AssetKey = AssetIndex,
    InstrumentKey = InstrumentIndex,
> = reconnect::Event<ExchangeId, AccountEvent<ExchangeKey, AssetKey, InstrumentKey>>;

/// Initialised [`ExecutionBuild`](builder::ExecutionBuild).
///
/// Contains execution component task handles as well as
/// [`ExecutionRequest`](request::ExecutionRequest) and [`AccountStreamEvent`] channels.
#[allow(missing_debug_implementations)]
pub struct Execution {
    pub execution_txs: MultiExchangeTxMap,
    pub account_channel: Channel<AccountStreamEvent>,
    pub handles: ExecutionHandles,
}
