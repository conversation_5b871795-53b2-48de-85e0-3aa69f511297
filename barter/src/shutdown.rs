use serde::{Deserialize, Serialize};

pub trait SyncShutdown {
    type Result;
    fn shutdown(&mut self) -> Self::Result;
}

pub trait AsyncShutdown {
    type Result;
    fn shutdown(&mut self) -> impl Future<Output = Self::Result>;
}

#[derive(
    <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>ial<PERSON>q, <PERSON>q, <PERSON>ial<PERSON>rd, Or<PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, Deserialize, Serialize,
)]
pub struct Shutdown;
