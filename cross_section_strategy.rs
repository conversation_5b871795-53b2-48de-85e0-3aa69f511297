impl AlgoStrategy<ExchangeId, InstrumentId> for CrossSectionStrategy {
    type State = EngineState<DefaultGlobalData, CrossSectionInstrumentData>;
    
    fn generate_algo_orders(&self, state: &Self::State) -> (...) {
        // 1. 计算所有品种的因子值
        let mut instrument_scores: Vec<_> = state.instruments
            .instruments(&InstrumentFilter::None)
            .map(|inst| {
                let score = self.calculate_composite_score(&inst.data.factors);
                (inst.instrument.id, score)
            })
            .collect();
        
        // 2. 截面排序选币
        instrument_scores.sort_by(|a, b| b.1.cmp(&a.1));
        let top_instruments = instrument_scores.into_iter()
            .take(self.max_positions)
            .collect::<Vec<_>>();
        
        // 3. 生成交易信号
        let orders = top_instruments.into_iter()
            .filter_map(|(instrument_id, _)| {
                self.generate_order_for_instrument(state, instrument_id)
            });
        
        (std::iter::empty(), orders)
    }
}