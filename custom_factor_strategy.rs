#[derive(<PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>)]
struct FactorData {
    momentum: Decimal,
    mean_reversion: Decimal,
    volume_factor: Decimal,
    volatility: Decimal,
    // 更多因子...
}

#[derive(<PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>)]
struct CrossSectionInstrumentData {
    market_data: DefaultInstrumentMarketData,
    factors: FactorData,
    rank_score: Option<Decimal>,
}